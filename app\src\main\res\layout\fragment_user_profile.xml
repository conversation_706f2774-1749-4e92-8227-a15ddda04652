<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    tools:context=".Frgaments.UserProfileFragment">

    <androidx.cardview.widget.CardView
        android:layout_width="match_parent"
        android:layout_height="250dp"
        app:cardCornerRadius="40dp"
        android:layout_margin="10dp"
        android:layout_centerInParent="true">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">


            <de.hdodenhof.circleimageview.CircleImageView
                android:id="@+id/UserProfileImg"
                android:layout_width="100dp"
                android:layout_height="100dp"
                android:src="@drawable/profile_icon" />


            <TextView
                android:id="@+id/UserNametxt"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_marginLeft="10dp"
                android:text="User Name"
                android:textSize="20dp"
                android:textStyle="bold" />
        </LinearLayout>

        <Button
            android:id="@+id/Signoutbtn"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="50dp"
            android:text="Sign Out"
            android:layout_gravity="center"
            />
    </androidx.cardview.widget.CardView>

</FrameLayout>