<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    tools:context=".Activities.RoleActivity">

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:textSize="30dp"
        android:textAlignment="center"
        android:layout_margin="20dp"
        android:textStyle="bold"
        android:text="Place, Choose Your Role Choose Your Role"/>


    <Button
        android:id="@+id/AdminBtn"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Admin"
        android:textSize="20dp"
        android:layout_margin="10dp"
        android:layout_gravity="center"
        />

    <Button
        android:id="@+id/UserBtn"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_margin="10dp"
        android:textSize="20dp"
        android:layout_gravity="center_horizontal"
        android:text="User"/>


</LinearLayout>