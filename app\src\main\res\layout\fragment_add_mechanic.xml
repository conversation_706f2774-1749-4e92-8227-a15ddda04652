<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    tools:context=".Frgaments.AddMechanicFragment">

    <EditText
        android:id="@+id/MechanicNameEditTxt"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="10dp"
        android:hint="Mechanic Name" />

    <EditText
        android:id="@+id/MechanicNumberEditTxt"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="10dp"
        android:hint="Mechanic Number" />

    <EditText
        android:id="@+id/VehicleNameEditTxt"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="10dp"
        android:hint="Vehicle Name" />

    <EditText
        android:id="@+id/LocationEditTxt"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="10dp"
        android:hint="Location" />


    <EditText
        android:id="@+id/PriceEditTxt"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="10dp"
        android:hint="Average Price(For 1hr)" />

    <Button
        android:id="@+id/AddNotificationBtn"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:layout_margin="10dp"
        android:text="Add Mechanic Details" />


</LinearLayout>