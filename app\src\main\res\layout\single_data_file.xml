<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:orientation="vertical">

    <androidx.cardview.widget.CardView
        android:layout_width="match_parent"
        android:layout_margin="10dp"
        app:cardCornerRadius="25dp"
        android:layout_height="wrap_content">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@color/backgroundColor"
            android:padding="20dp"
            android:orientation="vertical">

            <TextView
                android:id="@+id/NameTxt"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Mechanic Name"
                android:textSize="25dp"
                android:textStyle="bold" />

            <TextView
                android:id="@+id/PhoneNumberTxt"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Phone Number"
                android:textSize="25dp" />

            <TextView
                android:id="@+id/VehicleNameTxt"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Vehicle name"
                android:textSize="25dp"/>


            <TextView
                android:id="@+id/LocationTxt"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Location"
                android:textSize="25dp"/>


            <TextView
                android:id="@+id/PriceTxt"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="price"
                android:textSize="25dp"/>




        </LinearLayout>
    </androidx.cardview.widget.CardView>

</LinearLayout>